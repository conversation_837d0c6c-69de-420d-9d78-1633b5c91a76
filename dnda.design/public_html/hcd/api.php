<?php
session_start();
//unset($_SESSION['userdata']);
if(isset($_POST['text']))
{
    if(!isset($_SESSION['userdata']))
    {
        $_SESSION['userdata']=[];
    }
    $postCount = $_POST['postCount'];
    $url = 'https://askdndesign-api.demo.poweranalytics.com/secure/ask_qa';
    $data = array(
        "command" => $_POST['text'], 
        "timezone" => "America/Asuncion");
    
    $postdata = json_encode($data);
    
    $ch = curl_init($url); 
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); 
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'apikey: Ze3IaILjXUAlsww0Ore8jJPbOZls5pio'));
    $result = curl_exec($ch);
    curl_close($ch);

    $_SESSION['userdata'][$_POST['postCount']] = array(
       'title' => $_POST['text'],
       'text'  =>  $result
    );

    echo $result;
}
elseif(isset($_SESSION['userdata']))
{
    if(isset($_SESSION['userdata']))
    {
        echo json_encode($_SESSION['userdata']);
    }
    else
    {
        echo json_encode([]);
    }
    
}


?>