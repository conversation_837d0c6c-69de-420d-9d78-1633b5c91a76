<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Loading Animation</title>
<style>
    /* Basic styling for the loading container */
    .loading-container {
        width: 100%;
        max-width: 600px;
        margin: 20px auto;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Styling for each loading block */
    .loading-block {
        height: 20px;
        background-color: #f4dcb2;
        border-radius: 5px;
        animation: colorWave 1.5s infinite ease-in-out;
    }

    /* Variable widths to simulate different line lengths */
    .loading-block:nth-child(1) { width: 100%; animation-delay: 0s; }
    .loading-block:nth-child(2) { width: 90%; animation-delay: 0.1s; }
    .loading-block:nth-child(3) { width: 80%; animation-delay: 0.2s; }
    .loading-block:nth-child(4) { width: 70%; animation-delay: 0.3s; }
    .loading-block:nth-child(5) { width: 85%; animation-delay: 0.4s; }
    .loading-block:nth-child(6) { width: 95%; animation-delay: 0.5s; }
    .loading-block:nth-child(7) { width: 75%; animation-delay: 0.6s; }
    .loading-block:nth-child(8) { width: 60%; animation-delay: 0.7s; }
    .loading-block:nth-child(9) { width: 100%; animation-delay: 0.8s; }
    .loading-block:nth-child(10) { width: 90%; animation-delay: 0.9s; }

    /* Keyframes for the color wave animation */
    @keyframes colorWave {
        0% {
            background-color: #f4dcb2;
        }
        50% {
            background-color: #ffcc80;
        }
        100% {
            background-color: #f4dcb2;
        }
    }
</style>
</head>
<body>

<div class="loading-container">
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
    <div class="loading-block"></div>
</div>

</body>
</html>
