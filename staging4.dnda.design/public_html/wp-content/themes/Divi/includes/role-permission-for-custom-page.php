<?php
$page_id = get_the_ID(); // or manually set post/page ID

$allow_flag = get_post_meta($page_id, 'ure_prohibit_allow_flag', true);
$view_whom = get_post_meta($page_id, 'ure_content_view_whom', true);
$allowed_roles = get_post_meta($page_id, 'ure_content_for_roles', true);
$error_action = get_post_meta($page_id, 'ure_post_access_error_action', true);
$error_url = get_post_meta($page_id, 'ure_view_access_error_url', true);
$error_message = get_post_meta($page_id, 'ure_post_access_error_message', true);

// Default: allow access
$block_access = false;

if ($allow_flag === '2') {
    // Only proceed if "Allow View" is selected
    if ($view_whom === '2') {
        // Any logged-in user
        if (!is_user_logged_in()) {
            $block_access = true;
        }
    } elseif ($view_whom === '3') {
        // Selected roles
        if (!is_user_logged_in()) {
            $block_access = true;
        } else {
            $user = wp_get_current_user();
            $allowed_roles_array = array_map('trim', explode(',', $allowed_roles));
            $has_role = false;

            foreach ($user->roles as $role) {
                if (in_array($role, $allowed_roles_array)) {
                    $has_role = true;
                    break;
                }
            }

            if (!$has_role) {
                $block_access = true;
            }
        }
    }
}

// Handle restriction if needed
if ($block_access) {
    switch ($error_action) {
        case '1':
            // 404
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            include(get_query_template('404'));
            exit;

        case '2':
            // Default access error message
            wp_die('You do not have permission to view this page.');

        case '3':
            // Custom error message
            wp_die($error_message ? esc_html($error_message) : 'Access denied.');

        case '4':
            // Redirect
            if (!empty($error_url)) {
                wp_redirect(esc_url($error_url));
                exit;
            } else {
                wp_die('Access denied.');
            }

        default:
            wp_die('Access denied.');
    }
}
?>