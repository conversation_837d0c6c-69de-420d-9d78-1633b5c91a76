<?php
/*
Template Name: Custom Page By Rashid
*/
global $wpdb;
$user_id = get_current_user_id();

// $key = 'flash_' . get_current_user_id();
// $flash = get_transient($key);
// delete_transient($key);

get_header();
$field_value='';
if(isset($_GET['dec']))
{
    $field_value = urldecode($_GET['dec']);
}

?>
<?php


if(isset($_GET['frm_entry_delete_message']) && $_GET['frm_entry_delete_message'])
{
    echo '<div style="padding: 10%;"><h1>Entry has been deleted</h1>';
    echo '<div class="et_builder_inner_content et_pb_gutters3">
            <p><a href="/y25/applications/" role="link">Go back to the project list</a></p>
          </div></div>';
    get_footer();
    exit();
}

 $entry_id = '';
if(isset($_GET['entry'])){$entry_id=$_GET['entry'];}
$form_html= FrmFormsController::get_form_shortcode([
    'id' => 12,
    'entry_id' => $entry_id
]);


$field_66 ='[Project Name]';
$field_35 = '[Contact Name]';
$field_36 = '[Contact Email]';

if ( isset($_GET['app']) && is_numeric($_GET['app']) ) {
   $application_id=$_GET['app'];
   $field_66 = FrmEntryMeta::get_entry_meta_by_field($application_id, 66); // [Project Name]
   $field_35 = FrmEntryMeta::get_entry_meta_by_field($application_id, 35); // [Contact Name]
   $field_36 = FrmEntryMeta::get_entry_meta_by_field($application_id, 36); // [Contact Email]

   if(empty($field_66) || $field_66==NULL)
   {
      echo '<h1 style="padding: 10%;">Application not found</h1>';
      get_footer();
      exit();
   }

   if(!empty($field_35))
   {
      $field_35 = $field_35['first'].' '.$field_35['last'];
   }
}
$form_html= str_replace(
    ['[Contact Name]', '[Contact Email]', '[Project Name]'], 
    [$field_35       ,   $field_36      ,  $field_66],
    $form_html);
?>
<style>
.notice {
    padding: 15px 20px;
    margin: 20px 0;
    border-left: 4px solid #46b450;
    background-color: #ecf9f1;
    color: #1a531b;
    font-size: 14px;
    border-radius: 4px;
}

.notice.notice-success {
    border-left-color: #46b450;
    background-color: #ecf9f1;
    color: #1a531b;
}
</style>


<div style="padding: 5%;">
<h1>DNDA25 Result - Qualifying Project</h1>
<p style="padding-bottom:30px">
<strong>Project Name</strong>: <span id="project-name"><?=$field_66;?></span><br>
<strong>Contact</strong>: <span id="contact-name"><?=$field_36;?></span> &lt;<span id="contact-email"><?=$field_36;?></span>&gt;</p>

<?php
echo $form_html;

if ( $field_value === 'Full Project' ) {
    set_transient("y25_applications_tab_$user_id", 'Full Project');
    echo '<a id="project_list_a" href="/y25/applications/?tab=Full%20Project">Go back to the project list</a>';
} elseif ( $field_value === 'Qualified' ) {
    set_transient("y25_applications_tab_$user_id", 'Qualified');
    echo '<a id="project_list_a" href="/y25/applications/?tab=Qualified">Go back to the project list</a>';
} else{
    set_transient("y25_applications_tab_$user_id", 'Ineligible');
    echo '<a id="project_list_a" href="/y25/applications/?tab=Ineligible">Go back to the project list</a>';
}

?>

</div>

<?php get_footer();?>