<?php
/*
Plugin Name: DNDA Custom Plugin BY Rashid
Description: Custom functionality.
Version: 1.0
*/

// add_action('init', function() {
//     if($_POST)
//     {
//         echo '<pre>';
//         print_r($_POST);exit();
//     }
// });

function update_form_value_ras($entry_id,$field_id,$new_value)
{
    global $wpdb;
    $table = $wpdb->prefix . 'frm_item_metas';

    $meta_exists = $wpdb->get_var( $wpdb->prepare(
        "SELECT meta_value FROM $table WHERE item_id = %d AND field_id = %d",
        $entry_id, $field_id
    ));

    if ( $meta_exists !== null ) {
        $wpdb->update(
            $table,
            ['meta_value' => $new_value],
            ['item_id' => $entry_id, 'field_id' => $field_id]
        );
    } else {
        $wpdb->insert(
            $table,
            ['item_id' => $entry_id, 'field_id' => $field_id, 'meta_value' => $new_value]
        );
    }
}

add_filter('frm_submit_button', 'change_my_submit_button_label', 10, 2);
function change_my_submit_button_label($label, $form){
  if($label=='Update' && $form->id==12)
  {
    $label='Send Email';
  }
  return $label;
}

add_action('frm_after_create_entry', 'track_draft_on_formidable', 10, 2);
add_action('frm_after_update_entry', 'track_draft_on_formidable', 10, 2);
function track_draft_on_formidable($entry_id, $form_id) {
    if ($form_id != 12) return;

    global $wpdb;
    $entry = $wpdb->get_row(
        $wpdb->prepare("SELECT is_draft FROM {$wpdb->prefix}frm_items WHERE id = %d", $entry_id)
    );

    if ($entry && $entry->is_draft == 1) {
        // Save additional meta
        $wpdb->insert(
            $wpdb->prefix . 'frm_item_metas',
            [
                'item_id' => $entry_id,
                'field_id' => 311,
                'meta_value' => "Yes",
                'created_at' => current_time('mysql')
            ]
        );

        // Set is_draft = 0
        $wpdb->update(
            "{$wpdb->prefix}frm_items",
            ['is_draft' => 0],
            ['id' => $entry_id]
        );

        // Set flash message and redirect flag
        $user_id = get_current_user_id();
        set_transient("flash_$user_id", 'Your draft has been saved.', 30);
        set_transient("redirect_after_draft_$user_id", $entry_id, 30);
    }
}

add_action('template_redirect', 'maybe_trigger_draft_redirect');
function maybe_trigger_draft_redirect() {
    $user_id = get_current_user_id();
    $entry_id = get_transient("redirect_after_draft_$user_id");

    if ($entry_id) {
        delete_transient("redirect_after_draft_$user_id");

        add_action('wp_footer', function() use ($entry_id) {
            ?>
            <script>
                (function() {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('entry', '<?php echo esc_js($entry_id); ?>');
                    window.history.pushState({}, '', currentUrl);
                })();
            </script>
            <?php
        });
    }
}

// add_action('template_redirect', 'maybe_redirect_after_draft');
// function maybe_redirect_after_draft() {
//     $user_id = get_current_user_id();
//     $entry_id = get_transient("redirect_after_draft_$user_id");

//     if ($entry_id) {
//         // Remove the transient to prevent repeat redirect
//         delete_transient("redirect_after_draft_$user_id");

//         // Redirect to the current page with entry=ID
//         $redirect_url = add_query_arg('entry', $entry_id, wp_get_referer());
//         wp_safe_redirect($redirect_url);
//         exit;
//     }
// }

add_filter('wp_mail', 'check_form_submission_email_globally', 10, 1);
function check_form_submission_email_globally() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
        global $wpdb;
        
        if (isset($_POST['form_id']) && $_POST['form_id']==12 && isset($_GET['app'])) {

            $_POST['item_meta'][284] = trim($_POST['item_meta'][284]);
            
            $new_value = $_POST['item_meta'][284];

            if($_POST['item_meta'][284]=='Yes')
            {
               $new_value='Full Project';
            }
            elseif($_POST['item_meta'][284]=='No' && isset($_GET['dec']))
            {
               $new_value=trim(urldecode($_GET['dec']));
            }

            
            $entry_id = $_GET['app'];
            $table = $wpdb->prefix . 'frm_items';

            $user_id = $wpdb->get_var( $wpdb->prepare("
                SELECT user_id
                FROM $table
                WHERE id = %d
            ", $entry_id) );

            $user = get_userdata($user_id);

            if($new_value=='Full Project' && $user)
            {
                $wpdb->update(
                    $wpdb->prefix . 'frm_items',
                    ['is_draft' => 1],
                    ['form_id' => 5, 'id' => $entry_id]
                );
                
                update_form_value_ras($entry_id,312,"");
                $user->add_role('dnda25-project-finalist');
            }
            elseif($user)
            {
                update_form_value_ras($entry_id,312,"Sent Email");
                $user->remove_role('dnda25-project-finalist');
            }

            if($new_value!='No')
            {
                $entry_id = $_GET['app'];
                $field_id = 310;
                update_form_value_ras($entry_id,$field_id,$new_value);
            }
        }
    }
}
