<?php
/*
Template Name: Custom Page
*/
$category='';
$decision_list=[
  'Ineligible',
  'Qualified',
  'Wrong Category'
];

global $wpdb;

if($_POST)
{
  foreach($_POST['d'] as $entry_id=>$new_value)
  {
    if(!empty($new_value)) {
      $table = $wpdb->prefix . 'frm_item_metas';
      $field_id = 280;

      $table = $wpdb->prefix . 'frm_item_metas';

      $meta_exists = $wpdb->get_var( $wpdb->prepare(
            "SELECT meta_value FROM $table WHERE item_id = %d AND field_id = %d",
            $entry_id, $field_id
        ));
        
        if ( $meta_exists !== null ) {
            $wpdb->update(
                $table,
                ['meta_value' => $new_value],
                ['item_id' => $entry_id, 'field_id' => $field_id]
            );
        } else {
            $wpdb->insert(
                $table,
                ['item_id' => $entry_id, 'field_id' => $field_id, 'meta_value' => $new_value]
            );
        }
    }
      
  }
}
get_header();
?>
<style>
.email {
  border-bottom: 1px solid #ccc;
  padding: 20px 0;
  font-family: Arial, sans-serif;
}

.email-cell {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.cell-green {
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.email-cell p {
  margin: 4px 0;
  font-size: 14px;
  color: #000;
  width: 100%;
}

.email-cell p:nth-child(3) {
  font-weight: bold;
  font-size: 15px;
  color: #1a73e8;
  margin-top: 10px;
}

.email-cell span {
  font-size: 12px;
  color: #000;
}

.email-cell .date {
  font-size: 14px;
  color: #000;
  white-space: nowrap;
  margin-left: auto;
  margin-top: 4px;
}

blockquote, iframe {
  display: none !important;
}
</style>
<style>
  .feedback {
  display: flex;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.feedback-cell {
  padding: 10px;
  box-sizing: border-box;
}

.feedback-cell:first-child {
  width: 10%;
  background-color: #f9f9f9;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.feedback-cell:not(:first-child) {
  width: 90%;
  background-color: #fff;
}

.cell-green {
  background-color: #e6f7e6 !important;
}

.cell-red {
  background-color: #ffe6e6 !important;
}

.feedback-cell img {
  width: 20px;
  height: auto;
  border-radius: 50%;
}
</style>
<style>
    .top-header {
      /* display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap; */
    }

    .summary-cards {
      display: flex;
      gap: 10px;
    }

    .summary-card {
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 10px 16px;
      font-weight: bold;
      color: #666;
      font-size: 35px;
    }

    .summary-card span {
      font-size: 12px;
      font-weight: normal;
      float: right;
      line-height: 1.2;
      margin-left: 5px;
    }

    .summary-card.active {
      background-color: #2f67d8;
      color: #fff;
      border: none;
    }

    .summary-card.inactive {
      background-color: #f0f0f0;
      color: #ccc;
    }

    .filters {
      font-size: 15px;
      float: left;
    }

    .filters a {
      cursor: pointer;
    }

    .filters span {
      margin-right: 20px;
      font-weight: normal;
      color: #555;
    }

    .filters .active-tab {
      font-weight: bold;
      border-bottom: 2px solid black;
    }

    .search-group {
      display: flex;
      gap: 5px;
      margin-top: 10px;
    }

    .search-group input[type="text"] {
      padding: 8px 12px;
      font-size: 14px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 250px;
    }

    .search-group button {
      padding: 8px 18px;
      background: white;
      border: 2px solid #2f67d8;
      color: #2f67d8;
      font-weight: bold;
      font-size: 14px;
      border-radius: 8px;
      cursor: pointer;
    }

    .search-group button:hover {
      background: #2f67d8;
      color: white;
    }

    .update-button {
      float: right;
      padding: 10px 20px;
      font-size: 15px;
      font-weight: bold;
      background: white;
      border: 2px solid #2f67d8;
      color: #2f67d8;
      border-radius: 8px;
      cursor: pointer;
    }

    .update-button:hover {
      background-color: #2f67d8;
      color: white;
    }

    @media (max-width: 600px) {
      .top-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .search-group {
        width: 100%;
      }

      .search-group input {
        flex: 1;
      }
    }
  </style>
<style>
    .ras-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      border-radius:10px;
    }

    .ras-table th, .ras-table td {
      padding: 12px;
      vertical-align: top;
      font-size: 14px;
      border: none;
    }
    .ras-table tr{
      border: 1px solid #ddd;
    }

    .ras-table th {
      background-color: #2c3e50;
      color: white;
      text-align: left;
    }

    .ras-table .project-title {
      font-weight: bold;
      margin-bottom: 4px;
    }

    .ras-table .project-desc {
      font-size: 13px;
      color: #333;
    }

    .ras-table .actions {
      margin-top: 10px;
      font-size: 12px;
    }

    .ras-table .actions a {
      color: #007bff;
      text-decoration: none;
      margin-right: 8px;
    }

    .ras-table .actions a.disabled {
      color: #999;
      pointer-events: none;
    }

    .ras-table .eval-cell {
      display: flex;
      align-items: stretch;
      gap: 10px;
    }

    .ras-table .eval-score {
      font-weight: bold;
      color: #fff;
      min-width: 40px;
      text-align: center;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
    }

    .ras-table .eval-score.green { background-color: #4CAF50; }
    .ras-table .eval-score.red { background-color: #e74c3c; }
    .ras-table .eval-score.black { background-color: #333; }

    .ras-table .reviewers {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 13px;
    }

    .ras-table .reviewer {
      display: flex;
      align-items: center;
      margin-bottom: 2px;
    }

    .ras-table .reviewer::before {
      content: "✔️";
      margin-right: 5px;
      color: green;
    }

    .ras-table .reviewer.reject::before {
      content: "❌";
      color: red;
    }

    select {
      padding: 5px;
      width: 100%;
    }
    .eval-cell .reviewers img,.eval-cell .frm_image_option_size_small,.eval-cell .frm_has_image_options{
      width: 10px !important;
    }
    .eval-cell .Rtable{
      padding: 3px;
    }
    .eval-cell .Rtable .Rtable-cell:first-child {
      width: 20px !important;
      float: left;
    }
    .eval-cell .reviewers{
      width: 100%;
    }
    .row{
      width:100%;
      height:80px;
    }
    .bg-gray{
      background-color: #f4f4f4
    }
  </style>
<script>
  function showFeedback(id) {
    if ($("#feedback_"+id).is(":visible")) {
      $("#feedback_"+id).hide();
      $("#show_feedback_"+id).html('Show Feedback');
    } else {
      $("#feedback_"+id).show();
      $("#show_feedback_"+id).html('Hide Feedback');
    }
  }
  function showEmail(id) {
    if ($("#email_"+id).is(":visible")) {
      $("#email_"+id).hide();
      $("#show_email_"+id).html('Show Email');
    } else {
      $("#email_"+id).show();
      $("#show_email_"+id).html('Hide Email');
    }
  }
</script>
<div style="padding: 5%;">
<div class="top-header">
  <div>
    <div class="row">
      <h1 style="float: left;">DNDA25 Awards Applicants</h1>
      <div style="float: right;" class="search-group">
        <form method="GET" action="">
        <input required value="<?=isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';?>" type="text" name="search" placeholder="Search..." />
        <!-- <button type="submit">Search</button> -->
        </form>
      </div>
    </div>
    
    <div class="summary-cards">
      <a href=""><div class="summary-card active"><spam id="count_total"></spam> <span>Projects</span></div></a>
      <a href="#"><div class="summary-card"><spam id="count_0_1">0</spam> <span>HCD+<br>Education</span></div></a>
      <a href="#"><div class="summary-card"><spam id="count_3">0</spam> <span>Special<br>Recognition</span></div></a>
    </div>
    <div class="row">
      <br>
      
      <div class="filters">
        <a onclick="opentab(this,true)"><span class="tabs active active-tab" id="count_total2"><spam>0</spam> Projects</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Qualified</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Ineligible</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Full Project</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Rejected</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Finalist</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Shortlist</span></a>
        <a onclick="opentab(this)"><span class="tabs"><spam>0</spam> Laureates</span></a>
      </div>
      <button form="frm_form_123" type="submit" class="update-button">Update List</button>
    </div>
  </div>

    
</div>
<form id="frm_form_123" action="" method="post">
  <table class="ras-table">
      <thead>
        <tr>
          <th>Entry ID</th>
          <th>Decision</th>
          <th style="width: 350px;">Project Name</th>
          <th>Location</th>
          <th style="width: 120px;">Evaluation</th>
        </tr>
      </thead>
      <tbody>
          <?php
              // Load Formidable's entry class
              //$entries = FrmEntry::getAll(array('it.form_id' => 5), ' ORDER BY it.created_at ASC');

              $count_0=0;
              $count_1=0;
              $count_2=0;
              $count_3=0;

              $count_total = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                      INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                      WHERE 
                        it.form_id = %d
                        AND im.field_id = %d
                        AND im.meta_value != ''
                      ",
                      5,
                      66
                  )
              );

              $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
              $results = [];

              if ( ! empty($search) ) {
                  $results = $wpdb->get_col(
                      $wpdb->prepare(
                          "
                          SELECT DISTINCT it.id
                          FROM {$wpdb->prefix}frm_items it
                          INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                          WHERE 
                            it.form_id = %d
                            AND im.meta_value LIKE %s
                          ORDER BY it.created_at ASC
                          ",
                          5, // form_id
                          '%' . $wpdb->esc_like($search) . '%'
                      )
                  );
              } 
              else 
              {
                  $results = $wpdb->get_col(
                    $wpdb->prepare(
                        "
                        SELECT it.id
                        FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND im.field_id = %d
                          AND im.meta_value != ''
                        ORDER BY it.created_at ASC
                        ",
                        5, // form_id
                        66 // field_id
                    )
                );
              }

              
              
              // Now get full entries
              $bgcolor=true;
              foreach ( $results as $entry_id ) {
                  $bgcolor = !$bgcolor;
                  $entry = FrmEntry::getOne( $entry_id );

                  // Get field values using field IDs or keys
                  $field_66 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 66); // Project Title
                  $field_134 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 134);
                  $field_67 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 67);
                  $field_229 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 229);
                  $field_280 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 280);
                  $score = do_shortcode("[frm-math clean=1 decimal=1]([frm-stats id=251 type=count 236='Yes' 251='{$entry_id}'] / [frm-stats id=251 type=count 251='{$entry_id}'])[/frm-math]");
                  $score_cls = 'red';

                  //$feedback=do_shortcode("[display-frm-data id=2981 entry=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $feedback_data= do_shortcode('[display-frm-data id=2981 entry="' . $entry_id . '" filter=1 drafts="both"]');
                  $email_data=do_shortcode("[display-frm-data id=3100 entry=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $reviews_data=do_shortcode("[display-frm-data id=2665 entry=\"{$entry_id}\" filter=1]");

                  $tab_value=$field_280;


                  $query = $wpdb->prepare(
                    "
                    SELECT target.meta_value
                    FROM {$wpdb->prefix}frm_items AS fi
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS match_field
                        ON fi.id = match_field.item_id AND match_field.field_id = %d
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS target
                        ON fi.id = target.item_id AND target.field_id = %d
                    WHERE fi.form_id = %d
                      AND match_field.meta_value = %s
                    ",
                    283, // field_id = 283
                    262, // field_id = 262
                    12,
                    $entry_id // value of 283 must be 71
                );
                
                $meta_value = $wpdb->get_var($query);

                if($tab_value=='Qualified' && $meta_value=='Yes')
                {
                  $tab_value = 'Finalist';
                }
                  

                  //$reviews_data = FrmEntry::getOne( $entry_id, true );
          ?>    
        <tr data-num="<?=$tab_value;?>" id="row_<?=$entry_id;?>" style="<?=($bgcolor)?'background-color: #f4f4f4':'';?>">
          <td>P2500<?=$entry_id;?></td>
          <td>
            <select name="d[<?=$entry_id;?>]">
              <option value="">Select Decision</option>
              <?php foreach($decision_list as $row){?>
                <option <?=($field_280==$row)?'selected':'';?>><?=$row;?></option>
              <?php }?>
            </select>
          </td>
          <td>
            <div class="project-title"><?php echo $field_66;?></div>
            <div class="project-desc"><?=(!empty($field_229))?substr($field_229,0,100).'...':''; ?></div>
          </td>
          <td><?php echo $field_134; ?>, <?php echo $field_67; ?></td>
          <td>
            <div class="eval-cell">
              <div class="reviewers">
                <?=$reviews_data;?>
              </div>
            </div>
          </td>
        </tr>
        <tr id="feedback_<?php echo $entry_id; ?>" style="display:none;<?=($bgcolor)?'background-color: #f4f4f4;':'';?>" class="child-row">
          <td colspan="5"><?=$feedback_data; ?></td>
        </tr>
        <tr id="email_<?php echo $entry_id;?>" style="display:none;<?=($bgcolor)?'background-color: #f4f4f4;':'';?>" class="child-row">
          <td colspan="5"><?=$email_data;?></td>
        </tr>
        <tr id="rowchild_<?php echo $entry_id; ?>" style="<?=($bgcolor)?'background-color: #f4f4f4;':'';?>" class="child-row">
          <td></td>
          <td colspan="4">
            <div style="border-top: 1px solid #dcdbdb;">
              <div class="actions">
                <a href="/y25/pq-evaluation/?app=<?php echo $entry_id; ?>">Feedback Form</a> | 
                <a href="javascript:void(0);" onClick="showFeedback(<?php echo $entry_id; ?>);" id="show_feedback_<?php echo $entry_id; ?>">Show Feedback</a>
                <?php if('Qualified'==$field_280){?>
                  <span style="margin-left: 100px;"></span>
                  <a href="/y25/pq-result-email/?app=<?php echo $entry_id; ?>">Compose Email</a> | 
                  <a href="javascript:void(0);" onClick="showEmail(<?php echo $entry_id; ?>);" id="show_email_<?php echo $entry_id; ?>">Show Email</a>
                <?php }?>
              </div>
            </div>
          </td>
        </tr>
      <?php } ?>  

      </tbody>
    </table>  
  </form>  
</div>
<script>
function updateCounts() {
  $('.filters a').each(function() {
    var $a = $(this);
    var labelText = $a.find('.tabs').clone().children().remove().end().text().trim();

    var count = 0;

    // Count rows with matching data-num
    $('table tbody tr').each(function() {
      var num = $(this).data('num');
      if (num === labelText) {
        count++;
      }
    });

    // Show total count if label is 'Projects'
    if (labelText === 'Projects') {
      $a.find('span spam').text(<?=$count_total;?>); // Or use $('table tbody tr[data-num]').length
    } else {
      $a.find('span spam').text(count);
    }
  });
}  
// function updateCounts() {
//   $('.filters a').each(function() {
//     var $a = $(this);
//     var labelText = $a.text().trim();  
    
//     // Remove number part if exists
//     labelText = labelText.replace(/^\d+\s*/, '');

//     var count = 0;
//     $('table tbody tr td:nth-child(2) select').each(function() {
//       var selectedText = $(this).find('option:selected').text().trim();
//       if (selectedText === labelText) {
//         count++;
//       }
//     });
//     if(labelText=='Projects')
//     {
//       $a.find('span spam').text(<?=$count_total;?>);
//     }
//     else
//     {
//       $a.find('span spam').text(count);
//     }
//   });
// }


$(document).ready(function() {
  updateCounts();
  document.getElementById('count_total').innerHTML='<?=$count_total;?>';
  //$('#count_total2').find('span span').html(<?=$count_total;?>);
});
</script>
<script>
  var table;
  var childRows;
 jQuery(document).ready(function ($) {
  childRows = $('.child-row').detach();

  $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
    var requiredNum = window.currentNumFilter || '';
    if (!requiredNum) return true;

    var row = table.row(dataIndex).node();
    var num = $(row).data('num'); // get data-num attribute from the row
    return num == requiredNum;
  });

  table = $('.ras-table').DataTable({
    paging: true,
    searching: true,
    info: false,
    ordering: true,
    lengthChange: false,
    autoWidth: false,
    stripeClasses: [],
    dom: 'lrtip', // removes the default search box
    columnDefs: [
      {
        targets: 1, // Column with dropdown
        render: function (data, type, row, meta) {
          if (type === 'filter' || type === 'sort') {
            // Extract the selected value from the select element
            var selected = $(data).find('option:selected').text();
            return selected;
          }
          return data; // keep the original HTML for display
        }
      }
    ]
  });

  fillChildsB();

  $('input[name="search"]').on('keyup', function () {
    table.search(this.value).draw();
    fillChildsB();
  });

  $('.tabs').on('click', function () {
  var tabToShow = $(this).data('tab');

  // Remove active class from all buttons
  $('.tab-btn').removeClass('active');
  $(this).addClass('active');

  // Hide all tabs, then show the selected one
  $('.tab-content').hide();
  $(tabToShow).show();
});

});
function fillChildsB()
{
  childRows.each(function () {
    // Get the ID like feedback_45 or email_45
    var id = this.id;
    
    // Extract the entry ID (e.g., 45)
    var entryId = id.split('_')[1];

    // Insert after the main entry row
    $('#row_' + entryId).after(this); // You must set id="row_45" in the main row
  });
}
function opentab(element, clear = false) {
  $('.tabs').removeClass('active-tab');
  $(element).find('.tabs').addClass('active-tab');

  var val = $(element).find('.tabs').clone().children().remove().end().text().trim();

  if (!clear) {
    window.currentNumFilter = val;
  } else {
    window.currentNumFilter = '';
  }

  table.draw();
  fillChildsB();
}

// function opentab(element,clear=false) {
//   // Remove active class from all tabs
//   $('.tabs').removeClass('active-tab');

//   // Add active class to clicked one
//   $(element).find('.tabs').addClass('active-tab');

//   // Get the filter text (removing the count span)
//   var val = $(element).find('.tabs').clone() // clone to avoid affecting original
//     .children() // get children elements (like span)
//     .remove()   // remove them (e.g. the count)
//     .end()      // go back to cloned .tabs
//     .text()     // get the remaining text
//     .trim();    // clean up extra spaces

//   // Filter the table based on column 1
//   if(!clear)
//   {
//     table.column(1).search(val).draw();
//   }
//   else
//   {
//     table.column(1).search("").draw();
//   }
  
//   fillChildsB();
// }
</script>
<?php get_footer(); ?>