<?php
/*
Template Name: Org Recognition Tab Page
*/
global $wpdb;

$category='';
if(isset($_GET['tab']))
{
  $category=trim(urldecode($_GET['tab']));
}
$decision_list=[
  'Laureates',
  'Move to Education',
  'Move to Projects'
];

if ($_POST) {
  $table = $wpdb->prefix . 'frm_item_metas';
  $field_id = 280;

  foreach ($_POST['d'] as $entry_id => $new_value) {
      $entry_id = (int) $entry_id;
      $new_value = sanitize_text_field($new_value); // sanitize input
      $now = current_time('mysql'); // WordPress current time

      if($new_value=='Move to Projects')
      {
        $new_value = 'projects';
        $field_id = 284;
      }
      elseif($new_value=='Move to Education')
      {
        $new_value = 'HCD+ Education';
        $field_id = 284;
      }


      // Check if meta exists
      $meta_exists = $wpdb->get_var(
          $wpdb->prepare(
              "SELECT id FROM $table WHERE item_id = %d AND field_id = %d LIMIT 1",
              $entry_id, $field_id
          )
      );

      if ($meta_exists) {
          // Update existing meta
          $wpdb->update(
              $table,
              [
                  'meta_value' => $new_value,
                  'created_at' => $now
              ],
              [
                  'item_id' => $entry_id,
                  'field_id' => $field_id
              ]
          );
      } else {
          // Insert new meta
          $wpdb->insert(
              $table,
              [
                  'item_id' => $entry_id,
                  'field_id' => $field_id,
                  'meta_value' => $new_value,
                  'created_at' => $now
              ]
          );
      }
  }
}
get_header();
?>
<style>
.email {
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  padding: 20px;
  margin: 30px 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.email .email-cell {
  background-color: #ffffff;
  border-left: 5px solid #ccc;
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
}

.email .cell-red {
  border-left-color: #e74c3c;
  background-color: #fdecea;
}

.email .cell-green {
  border-left-color: #2ecc71;
  background-color: #eafaf1;
}

.email a {
  color: #3498db;
  text-decoration: none;
}

.email a:hover {
  text-decoration: underline;
}

.email p {
  margin-bottom: 12px;
}

.email hr {
  border: none;
  border-top: 2px solid #ccc;
  margin: 40px 0;
}

.email strong,
.email b {
  color: #c0392b;
}

@media (max-width: 600px) {
  .email {
    padding: 15px;
    font-size: 13px;
  }

  .email .email-cell {
    padding: 12px;
  }
}
</style>
<style>
  .feedback {
  display: flex;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.feedback-cell {
  padding: 10px;
  box-sizing: border-box;
}

.feedback-cell:first-child {
  width: 10%;
  background-color: #f9f9f9;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.feedback-cell:not(:first-child) {
  width: 90%;
  background-color: #fff;
}

.cell-green {
  background-color: #e6f7e6 !important;
}

.reviewers .cell-red {
  background-color: #ffe6e6 !important;
  border: 1px solid red;
  border-radius: 15px;
  line-height: 2;
  max-width: 18px !important;
  margin-top: 5px;
}
.reviewers .cell-red p{
  font-size: 8px;
  text-align: center;
}

.feedback-cell img {
  width: 20px;
  height: auto;
  border-radius: 50%;
}
</style>
<style>
    .top-header {
      /* display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap; */
    }

    .summary-cards {
      display: flex;
      gap: 10px;
    }

    .summary-card {
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 10px 16px;
      font-weight: bold;
      color: #666;
      font-size: 35px;
    }

    .summary-card span {
      font-size: 12px;
      font-weight: normal;
      float: right;
      line-height: 1.2;
      margin-left: 5px;
    }

    .summary-card.active {
      background-color: #2f67d8;
      color: #fff;
      border: none;
    }

    .summary-card.inactive {
      background-color: #f0f0f0;
      color: #ccc;
    }

    .filters {
      font-size: 15px;
      float: left;
    }

    .filters a {
      cursor: pointer;
    }

    .filters span {
      margin-right: 20px;
      font-weight: normal;
      color: #555;
    }

    .filters .active-tab {
      font-weight: bold;
      border-bottom: 2px solid black;
    }

    .search-group {
      display: flex;
      gap: 5px;
      margin-top: 10px;
    }

    .search-group input[type="text"] {
      padding: 8px 12px;
      font-size: 14px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 250px;
    }

    .search-group button {
      padding: 8px 18px;
      background: white;
      border: 2px solid #2f67d8;
      color: #2f67d8;
      font-weight: bold;
      font-size: 14px;
      border-radius: 8px;
      cursor: pointer;
    }

    .search-group button:hover {
      background: #2f67d8;
      color: white;
    }

    .update-button {
      float: right;
      padding: 10px 20px;
      font-size: 15px;
      font-weight: bold;
      background: white;
      border: 2px solid #2f67d8;
      color: #2f67d8;
      border-radius: 8px;
      cursor: pointer;
    }

    .update-button:hover {
      background-color: #2f67d8;
      color: white;
    }

    @media (max-width: 600px) {
      .top-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .search-group {
        width: 100%;
      }

      .search-group input {
        flex: 1;
      }
    }
  </style>
<style>
    .ras-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      border-radius:10px;
    }

    .ras-table th, .ras-table td {
      padding: 12px;
      vertical-align: top;
      font-size: 18px;
      border: none;
    }
    .ras-table tr{
      /* border: 1px solid #ddd; */
    }

    .ras-table th {
      background-color: #2c3e50;
      color: white;
      text-align: left;
    }

    .ras-table .project-title {
      font-weight: bold;
      margin-bottom: 4px;
    }

    .ras-table .project-desc {
      font-size: 20px;
      color: #333;
    }

    .ras-table .actions {
      margin-top: 10px;
      font-size: 18px;
    }

    .ras-table .actions a {
      color: #007bff;
      text-decoration: none;
      margin-right: 8px;
    }

    .ras-table .actions a.disabled {
      color: #999;
      pointer-events: none;
    }

    .ras-table .eval-cell {
      display: flex;
      align-items: stretch;
      gap: 10px;
    }

    .ras-table .eval-score {
      font-weight: bold;
      color: #fff;
      min-width: 40px;
      text-align: center;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
    }

    .ras-table .eval-score.green { background-color: #4CAF50; }
    .ras-table .eval-score.red { background-color: #e74c3c; }
    .ras-table .eval-score.black { background-color: #333; }

    .ras-table .reviewers {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 20px;
    }

    .ras-table .reviewer {
      display: flex;
      align-items: center;
      margin-bottom: 2px;
    }

    .ras-table .reviewer::before {
      content: "✔️";
      margin-right: 5px;
      color: green;
    }

    .ras-table .reviewer.reject::before {
      content: "❌";
      color: red;
    }

    select {
      padding: 5px;
      width: 100%;
    }
    .eval-cell .reviewers img,.eval-cell .frm_image_option_size_small,.eval-cell .frm_has_image_options{
      width: 10px !important;
    }
    .eval-cell .Rtable{
      padding: 3px;
    }
    .eval-cell .Rtable .Rtable-cell:first-child {
      width: 20px !important;
      float: left;
      margin-right: 7px;
    }
    .eval-cell .reviewers{
      width: 100%;
    }
    .row{
      width:100%;
      height:80px;
    }
    .bg-gray{
      background-color: #f4f4f4
    }
  </style>
<script>
  function showFeedback(id) {
    if ($("#feedback_"+id).is(":visible")) {
      $("#feedback_"+id).hide();
      $("#show_feedback_"+id).html('View Application');
    } else {
      $("#feedback_"+id).show();
      $("#show_feedback_"+id).html('Hide Application');
    }
  }
  function showEmail(id) {
    if ($("#email_"+id).is(":visible")) {
      $("#email_"+id).hide();
      $("#show_email_"+id).html('View Email');
    } else {
      $("#email_"+id).show();
      $("#show_email_"+id).html('Hide Email');
    }
  }
</script>
<div style="padding: 5%;">
<div class="top-header">
  <div>
    <div class="row">
      <h1 style="float: left;">DNDA25 Awards Applicants</h1>
      <div style="float: right;" class="search-group">
        <form method="GET" action="">
        <input type="hidden" name="tab" value="<?=$category;?>"/>  
        <input required value="<?=isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';?>" type="text" name="search" placeholder="Search..." />
        <button type="submit">Search</button>
        </form>
      </div>
    </div>
    
    <div class="summary-cards">
      <a href="/projects/"><div class="summary-card"><spam id="count_total">0</spam> <span>Projects</span></div></a>
      <a href="/hcd-education/"><div class="summary-card"><spam id="count_total_1">0</spam> <span>HCD+<br>Education</span></div></a>
      <a href="/org-recognition/"><div class="summary-card active"><spam id="count_total_2">0</spam> <span>Organization<br>Recognition</span></div></a>
    </div>
    <div class="row">
      <br>
      
      <div class="filters">
        <a href="/org-recognition/"><span class="tabs <?=($category==''?'active-tab':'');?>"><spam id="tab1">0</spam> Organization Recognition</span></a>
        <a href="/org-recognition/?tab=Laureates"><span class="tabs <?=($category=='Laureates'?'active-tab':'');?>"><spam id="tab2">0</spam> Laureates</span></a>
      </div>
      <button form="frm_form_123" type="submit" class="update-button">Update List</button>
    </div>
  </div>

    
</div>
<form id="frm_form_123" action="" method="post">
  <table class="ras-table">
      <thead>
        <tr>
          <th>Entry ID</th>
          <th>Decision</th>
          <th style="width: 350px;">Project Name</th>
          <th>Location</th>
          <th>Country</th>
          <th style="width: 200px;">Evaluation</th>
        </tr>
      </thead>
      <tbody>
          <?php
              // Load Formidable's entry class
              //$entries = FrmEntry::getAll(array('it.form_id' => 5), ' ORDER BY it.created_at ASC');

              $count_0=0;
              $count_1=0;
              $count_2=0;

              $tab1=0;
              $tab2=0;
              $tab3=0;
              $tab4=0;
              $tab5=0;
              $tab6=0;
              $tab7=0;
              $tab8=0;

              $count_0 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                          AND im.field_id = %d
                          AND im.meta_value = 'Projects'
                        ORDER BY it.created_at ASC
                        ",
                        5, // form_id
                        284 // field_id
                  )
              );
              $count_1 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                          AND im.field_id = %d
                          AND im.meta_value = 'HCD+ Education'
                        ORDER BY it.created_at ASC
                        ",
                        5, // form_id
                        284 // field_id
                  )
              );
              $count_2 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                          AND im.field_id = %d
                          AND im.meta_value = 'Org Recognition'
                        ORDER BY it.created_at ASC
                        ",
                        5, // form_id
                        284 // field_id
                  )
              );

              $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
              $results = [];

              if ( ! empty($search) && false) {
                  $results = $wpdb->get_col(
                      $wpdb->prepare(
                          "
                          SELECT DISTINCT it.id
                          FROM {$wpdb->prefix}frm_items it
                          INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                          WHERE 
                            it.form_id = %d
                            AND it.is_draft = 0
                            AND im.meta_value = 'Org Recognition'
                            AND im.meta_value LIKE %s
                          ORDER BY it.created_at ASC
                          ",
                          5, // form_id
                          '%' . $wpdb->esc_like($search) . '%'
                      )
                  );
              } 
              else 
              {
                  $results = $wpdb->get_col(
                    $wpdb->prepare(
                        "
                        SELECT it.id
                        FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                          AND im.field_id = %d
                          AND im.meta_value = 'Org Recognition'
                        ORDER BY it.created_at ASC
                        ",
                        5, // form_id
                        284 // field_id
                    )
                );
              }

              
              
              // Now get full entries
              $bgcolor=true;
              foreach ( $results as $entry_id ) {
                  $entry = FrmEntry::getOne( $entry_id );

                  // Get field values using field IDs or keys
                  $field_66 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 66); // Project Title
                  $field_134 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 134);
                  $field_67 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 67);
                  $field_229 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 229);
                  $field_280 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 280);
                  $score = do_shortcode("[frm-math clean=1 decimal=1]([frm-stats id=251 type=count 236='Yes' 251='{$entry_id}'] / [frm-stats id=251 type=count 251='{$entry_id}'])[/frm-math]");
                  $score_cls = 'red';

                  //$feedback=do_shortcode("[display-frm-data id=2981 entry=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $feedback_data= do_shortcode('[display-frm-data id=2981 entry="' . $entry_id . '" filter=1 drafts="both"]');
                  $email_data=do_shortcode("[display-frm-data id=3100 entry=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $reviews_data=do_shortcode("[display-frm-data id=2665 entry=\"{$entry_id}\" filter=1]");

                  $email_data_count = preg_match_all('/class="email"/', $email_data);

                  $tab_value=$field_280;


                  $query = $wpdb->prepare(
                    "
                    SELECT target.meta_value
                    FROM {$wpdb->prefix}frm_items AS fi
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS match_field
                        ON fi.id = match_field.item_id AND match_field.field_id = %d
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS target
                        ON fi.id = target.item_id AND target.field_id = %d
                    WHERE fi.form_id = %d
                      AND match_field.meta_value = %s
                    ",
                    259,
                    262,
                    12,
                    $entry_id
                );
                
                $meta_value = $wpdb->get_var($query);

                // if($tab_value=='Qualified' && $meta_value=='Yes')
                // {
                //   $tab_value = 'Finalist';
                // }
                $temp_field = $field_280;

                switch ($temp_field) {
                  case "":
                    $tab1++;
                    break;
                  case "Laureates":
                    $tab2++;
                    break;
                  case "Ineligible":
                    $tab3++;
                    break;
                  default:
                    $tab1++;
                    $temp_field = '';
                }

                if($category!=$temp_field)
                {
                  continue;
                }
                if(!empty($search))
                {
                  if(strpos(strtolower($field_66),strtolower($search))===false && strpos(strtolower($field_67),strtolower($search))===false && strpos(strtolower($field_134),strtolower($search))===false)
                  {
                    continue;
                  }
                }
                




                $bgcolor = !$bgcolor;


                  

                  //$reviews_data = FrmEntry::getOne( $entry_id, true );
          ?>    
        <tr data-num="<?=$tab_value;?>" id="row_<?=$entry_id;?>" style="<?=($bgcolor)?'border-top: 1px solid gray;background-color: #f4f4f4;':'';?>">
          <td><strong>P2500<?=$entry_id;?></strong></td>
          <td>
            <select name="d[<?=$entry_id;?>]">
              <option value="">Select Decision</option>
              <?php foreach($decision_list as $row){?>
                <option <?=($field_280==$row)?'selected':'';?>><?=$row;?></option>
              <?php }?>
            </select>
          </td>
          <td>
            <div class="project-title"><?php echo $field_66;?></div>
            <!-- <div class="project-desc"><?=(!empty($field_229))?substr($field_229,0,100).'...':''; ?></div> -->
          </td>
          <td><?php echo $field_67; ?></td>
          <td><?php echo $field_134; ?></td>
          <td>
            <div class="eval-cell">
              <div class="reviewers">
                <?=$reviews_data;?>
              </div>
            </div>
          </td>
        </tr>
        <tr id="rowchild_<?php echo $entry_id; ?>" style="<?=($bgcolor)?'border-bottom: 1px solid gray;background-color: #f4f4f4;':'';?>" class="child-row">
          <td></td>
          <td colspan="5">
            <div style="border-top: 1px solid #dcdbdb;">
              <div class="actions">
                
              <a href="/y25/pq-evaluation/?app=<?php echo $entry_id; ?>">Judges Responses</a> | 
                <a href="javascript:void(0);" onClick="showFeedback(<?php echo $entry_id; ?>);" id="show_feedback_<?php echo $entry_id; ?>">View Application</a>
                
                <?php if(false){?>
                  <span style="margin-left: 100px;"></span>
                  <a href="/y25/pq-result-email/?app=<?php echo $entry_id; ?>">Compose Email</a> | 
                  <a href="javascript:void(0);" onClick="showEmail(<?php echo $entry_id; ?>);" id="show_email_<?php echo $entry_id; ?>">View Email</a>(<?=$email_data_count;?>)
                <?php }?>

              </div>
            </div>
          </td>
        </tr>
        <tr id="email_<?php echo $entry_id;?>" style="display:none;<?=($bgcolor)?'background-color: #f4f4f4;':'';?>" class="child-row">
          <td colspan="5"><?=$email_data;?></td>
        </tr>
        <tr id="feedback_<?php echo $entry_id; ?>" style="display:none;<?=($bgcolor)?'background-color: #f4f4f4;':'';?>" class="child-row">
          <td colspan="5"><?=$feedback_data; ?></td>
        </tr>
      <?php } ?>  

      </tbody>
    </table>  
  </form>  
</div>
<script>
$(document).ready(function() {
  $('#count_total').html('<?=$count_0;?>');
  $('#count_total_1').html('<?=$count_1;?>');
  $('#count_total_2').html('<?=$count_2;?>');
  $('#tab1').html('<?=$tab1;?>');
  $('#tab2').html('<?=$tab2;?>');
  $('#tab3').html('<?=$tab3;?>');
  $('#tab4').html('<?=$tab4;?>');
  $('#tab5').html('<?=$tab5;?>');
  $('#tab6').html('<?=$tab6;?>');
  $('#tab7').html('<?=$tab7;?>');
  $('#tab8').html('<?=$tab8;?>');
});
</script>
<?php get_footer(); ?>