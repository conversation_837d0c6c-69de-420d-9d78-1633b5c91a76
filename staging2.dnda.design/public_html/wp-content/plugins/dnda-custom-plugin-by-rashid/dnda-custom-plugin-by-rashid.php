<?php
/*
Plugin Name: DNDA Custom Plugin BY Rashid
Description: Custom functionality.
Version: 1.0
*/

// add_action('init', 'check_form_submission_globally');
add_filter('wp_mail', 'check_form_submission_globally', 10, 1);

function check_form_submission_globally() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST)) {
        global $wpdb;
        
        if (isset($_POST['form_id']) && $_POST['form_id']==12 && isset($_GET['app'])) {
            
            $new_value = ($_POST['item_meta'][262]=='Yes')?'Full Project':$_POST['item_meta'][262];
            
            $entry_id = $_GET['app'];
            $table = $wpdb->prefix . 'frm_items';

            $user_id = $wpdb->get_var( $wpdb->prepare("
                SELECT user_id
                FROM $table
                WHERE id = %d
            ", $entry_id) );

            $user = get_userdata($user_id);

            if($new_value=='Full Project' && $user)
            {
                $user->add_role('dnda25-project-finalist');
            }
            elseif($user)
            {
                $user->remove_role('dnda25-project-finalist');
            }

            if(true)
            {
                $entry_id = $_GET['app'];
                $field_id = 280;

                $table = $wpdb->prefix . 'frm_item_metas';

                $meta_exists = $wpdb->get_var( $wpdb->prepare(
                        "SELECT meta_value FROM $table WHERE item_id = %d AND field_id = %d",
                        $entry_id, $field_id
                    ));

                    if ( $meta_exists !== null ) {
                        $wpdb->update(
                            $table,
                            ['meta_value' => $new_value],
                            ['item_id' => $entry_id, 'field_id' => $field_id]
                        );
                    } else {
                        $wpdb->insert(
                            $table,
                            ['item_id' => $entry_id, 'field_id' => $field_id, 'meta_value' => $new_value]
                        );
                    }
            }
        }
    }
}