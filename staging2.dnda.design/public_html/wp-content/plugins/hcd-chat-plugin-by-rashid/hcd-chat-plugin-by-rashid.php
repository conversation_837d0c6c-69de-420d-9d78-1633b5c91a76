<?php
/*
Plugin Name: HCD Chat Plugin By Rashid
Description: A plugin to display a chat icon and load a chat box on click with an iframe.
Version: 1.2
Author: <PERSON>
*/

function chat_icon_plugin_enqueue_scripts() {
    $plugin_url = plugins_url('', __FILE__);
    ?>
    <style>
        /* Chat Icon */
        #chat-icon {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            /* background-color: #007bff; */
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        #chat-icon-img {
            width: 100%;
            height: 100%;
            background-image: url('<?php echo $plugin_url; ?>/icon.png');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 50%;
        }

        #chat-box {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 400px;
            height: 540px;
            background-color: white;
            border: 1px solid #ccc;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            display: none;
            border-radius: 10px;
            overflow: hidden;
        }

        /* Update iframe height to fit the new box size */
        #chat-box iframe {
            width: 100%;
            height: 510px; /* Adjusted to fit the new height */
            border: none;
        }

        /* Chat Box Header */
        #chat-box-header {
            background: #986E24;
            color: white;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            font-size: 16px;
            font-weight: bold;
        }

        #chat-box-logo {
            height: 30px;
            width: auto;
            margin-top: 7px;
        }

        #chat-box-close {
            cursor: pointer;
            font-size: 18px;
            padding: 10px;
            margin-top: -3px;
        }
    </style>

    <script>
        jQuery(document).ready(function($) {
            $('#chat-icon').click(function() {
                $('#chat-box').toggle();
            });

            $('#chat-box-close').click(function() {
                $('#chat-box').hide();
            });
        });
    </script>
    <?php
}
add_action('wp_head', 'chat_icon_plugin_enqueue_scripts');

function chat_icon_plugin_html() {
    if (!is_user_logged_in()) {
        return;
    }

    // Check if user has the 'doti' role
    if ( !current_user_can('doti') ) {
        return;
    }

    $plugin_url = plugins_url('', __FILE__);
    ?>
    <div id="chat-icon">
        <div id="chat-icon-img"></div>
    </div>
    <div id="chat-box">
        <div id="chat-box-header">
            <img id="chat-box-logo" src="<?php echo $plugin_url; ?>/logo.png" alt="Logo">
            <span id="chat-box-close">x</span>
        </div>
        <iframe src="https://staging2.dnda.design/hcd/"></iframe>
    </div>
    <?php
}
add_action('wp_footer', 'chat_icon_plugin_html');