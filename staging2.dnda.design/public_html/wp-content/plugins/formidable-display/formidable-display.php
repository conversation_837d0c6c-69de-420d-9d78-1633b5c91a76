<?php
/**
 * Plugin Name: Formidable Data Page
 * Description: Show Formidable Forms data on a custom URL.
 * Version: 1.0
 * Author: <PERSON>
 */

// Register custom URL rewrite rule
add_action('init', function() {
    add_rewrite_rule('^formidable-data/?$', 'index.php?formidable_data_page=1', 'top');
});

// Register custom query var
add_filter('query_vars', function($vars) {
    $vars[] = 'formidable_data_page';
    return $vars;
});

// Load content for our custom page
add_action('template_redirect', function() {
    if (get_query_var('formidable_data_page')) {
        show_formidable_entries();
        exit;
    }
});

// Flush rewrite rules on plugin activation
register_activation_hook(__FILE__, function() {
    add_rewrite_rule('^formidable-data/?$', 'index.php?formidable_data_page=1', 'top');
    flush_rewrite_rules();
});

// Flush on deactivation too
register_deactivation_hook(__FILE__, 'flush_rewrite_rules');

// Main display function
function show_formidable_entries() {
    // Optional: Secure it by checking if Formidable is active
    if (!class_exists('FrmEntry')) {
        wp_die('Formidable Forms plugin is not active.');
    }

    $form_id = 5; // Change this to your actual Form ID
    $limit = 10;

    $entries = FrmEntry::getAll([
        'it.form_id' => $form_id,
        'limit' => $limit
    ]);

    $fields = FrmField::get_all_for_form($form_id);

    // Load theme header
    get_header();

    echo '<div class="wrap">';
    echo '<h2>Formidable Form Entries</h2>';

    if (empty($entries)) {
        echo '<p>No entries found.</p>';
    } else {
        echo '<table style="width:100%; border-collapse: collapse;" border="1">';
        echo '<thead><tr>';
        foreach ($fields as $field) {
            echo '<th style="padding:8px; background:#f4f4f4;">' . esc_html($field->name) . '</th>';
        }
        echo '</tr></thead><tbody>';

        foreach ($entries as $entry) {
            echo '<tr>';
            foreach ($fields as $field) {
                $value = FrmEntryMeta::get_entry_meta_by_field($entry->id, $field->id, true);
                echo '<td style="padding:8px;">' . esc_html($value) . '</td>';
            }
            echo '</tr>';
        }

        echo '</tbody></table>';
    }

    echo '</div>';

    // Load theme footer
    get_footer();

    exit;
}