<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * Localized language
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'dbsmmaqkymrhd2' );

/** Database username */
define( 'DB_USER', 'uo7slhwpwcoy5' );

/** Database password */
define( 'DB_PASSWORD', 'cnbaocj480ci' );

/** Database hostname */
define( 'DB_HOST', '127.0.0.1' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',          'a;IJURwWwLueUt?b!- !SaefLqw%,k0W(,^qV/}|xFaHo^)oD,*y|(5+VOs2b%1f' );
define( 'SECURE_AUTH_KEY',   '|HXmsmub0v^`~+hkbpIMvW?jvUId!JOh4+w|mZB5$f3pv#M83U1[r~JXV<[!M!cx' );
define( 'LOGGED_IN_KEY',     'KlUWk)/R-f:**v`OYSH,_euKsOm}<Tj&;or~U]wZ](-=VeXiI{,6(R5)(qcT#k>m' );
define( 'NONCE_KEY',         'Mq1&e?GzG$01}M@^&f8%Z5/dWnDgreCV>3u*MpZY/pC?(M48~0YH&}Sajr<bZ9.#' );
define( 'AUTH_SALT',         'GsIhjAdI3!dtQDTLfB61rHW2/%f&k|&R/Ke}+w-(*~VYdA`|I -iKw-E$E!aSpig' );
define( 'SECURE_AUTH_SALT',  '-Jgwd&0+FgJr*f[Gg48n6|c;<_;)7P+gg@Bqh+4;X!hHzj/Bx94/(X[BXJ~%/fj[' );
define( 'LOGGED_IN_SALT',    '+$}2K^&o%(f|HI60{ID:.5gU6HTNqQsA_<}$zC]!ffe>r-q_^*rTzTJPzVs??q`b' );
define( 'NONCE_SALT',        'E{eI-XT#coes@f,,k2,#D@GAcmNKAaqh>C|5`mx7^(;./B6Y`_NpaXcT,g1UD*m&' );
define( 'WP_CACHE_KEY_SALT', 'TCT0E !N1_{XM R2s)[zkhB<6xs(`t16Sssnp,+O(4c2^_iojNZn#hOI*;LbY*)V' );


/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'bbg_';


/* Add any custom values between this line and the "stop editing" line. */



/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://wordpress.org/support/article/debugging-in-wordpress/
 */
if ( ! defined( 'WP_DEBUG' ) ) {
	define( 'WP_DEBUG', false );
}

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
define( 'WP_ENVIRONMENT_TYPE', 'staging' ); // Added by SiteGround WordPress Staging system
@include_once('/var/lib/sec/wp-settings-pre.php'); // Added by SiteGround WordPress management system
require_once ABSPATH . 'wp-settings.php';
@include_once('/var/lib/sec/wp-settings.php'); // Added by SiteGround WordPress management system
