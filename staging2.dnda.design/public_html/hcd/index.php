<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCD+ Webpage</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <style>
        /* Background image styling */
        .main-content {
            /* height: 100vh; */
            padding-bottom: 10%;
            font-family: Arial, sans-serif;
            /* background: url('assets/images/hero_1.png') no-repeat center top; */
            /* background-size: 100% 100%; */
            color: #fff;
            position: relative;
        }

        /* Overlay to add padding at the top */
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 20vh;
            background-color: rgba(255, 255, 255, 0.5);
            z-index: 0;
        }

        /* Header styling */
        .header {
            padding: 10px 40px;
            position: fixed;
            z-index: 6;
            top: 0;
            width: 100%;
            background-color: #F2F2F2;
        }

        /* Help button styling */
        .help-btn {
            color: #0099ff;
            background-color: rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
        }

        /* Main content styling */
        .main-content {
            text-align: center;
            z-index: 1;
        }

        /* Footer styling */
        /* .footer {
            color: #ddd;
            padding-bottom: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        } */
        .img-btn{
            width: 40px;
            height: 40px;
            cursor: pointer;
            border: var(--bs-border-width) solid var(--bs-border-color);
            border-radius: 30px !important;
        }
        textarea::placeholder {
            color: #e1e1e1 !important;
        }
        .ras-search-bottom{
            position: absolute;
            bottom: 0;

        }
        .ras-search{
            padding: 0 15px 0 15px;
            max-width: 750px; 
            position: fixed; 
            bottom: -17px; 
            padding-bottom: 17px; 
            background-color: rgb(255, 255, 255);
        }
        textarea {
            width: 100%;
            border: none;
            resize: none;
            overflow: hidden; /* Hide scrollbars */
            font-size: 16px;
            line-height: 1.5;
            padding: 10px;
            outline: none;
            background-color: #f5f5f5;
        }
    /* Basic styling for the loading container */
    .loading-container {
        width: 100%;
        max-width: 600px;
        margin: 20px auto;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Styling for each loading block */
    .loading-block {
        height: 20px;
        background-color: #f4dcb2;
        border-radius: 5px;
        animation: colorWave 1.5s infinite ease-in-out;
    }

    /* Variable widths to simulate different line lengths */
    .loading-block:nth-child(1) { width: 100%; animation-delay: 0s; }
    .loading-block:nth-child(2) { width: 90%; animation-delay: 0.5s; }
    .loading-block:nth-child(3) { width: 80%; animation-delay: 0.2s; }
    .loading-block:nth-child(4) { width: 70%; animation-delay: 0.3s; }
    .loading-block:nth-child(5) { width: 85%; animation-delay: 0.4s; }
    .loading-block:nth-child(6) { width: 95%; animation-delay: 0.5s; }
    .loading-block:nth-child(7) { width: 75%; animation-delay: 0.6s; }
    .loading-block:nth-child(8) { width: 60%; animation-delay: 0.7s; }
    .loading-block:nth-child(9) { width: 100%; animation-delay: 0.8s; }
    .loading-block:nth-child(10) { width: 90%; animation-delay: 0.9s; }
    .loading-block:nth-child(11) { width: 100%; animation-delay: 0s; }
    .loading-block:nth-child(12) { width: 90%; animation-delay: 0.1s; }
    .loading-block:nth-child(13) { width: 80%; animation-delay: 0.2s; }
    .loading-block:nth-child(14) { width: 70%; animation-delay: 0.3s; }
    .loading-block:nth-child(15) { width: 85%; animation-delay: 0.4s; }
    .loading-block:nth-child(16) { width: 95%; animation-delay: 0.5s; }
    .loading-block:nth-child(17) { width: 75%; animation-delay: 0.6s; }
    .loading-block:nth-child(18) { width: 60%; animation-delay: 0.7s; }
    .loading-block:nth-child(19) { width: 100%; animation-delay: 0.8s; }
    .loading-block:nth-child(20) { width: 90%; animation-delay: 0.9s; }

    /* Keyframes for the color wave animation */
    @keyframes colorWave {
        0% {
            background-color: #f4dcb2;
        }
        50% {
            background-color: #ffcc80;
        }
        100% {
            background-color: #f4dcb2;
        }
    }
<style>
    /* Center the popup */
    .popup-container {
        position: fixed;
        max-width: 600px;
        background-color: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 50px auto;
        font-family: Arial, sans-serif;
        top: 0;
        z-index: 9999;
        right: 40px;
    }

    /* Close button */
    .close-button {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 18px;
        color: #888;
        cursor: pointer;
        border: none;
        background: none;
    }

    /* Header and description styling */
    .popup-header {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .popup-description {
        font-size: 14px;
        color: #555;
        line-height: 1.5;
        margin-bottom: 20px;
    }

    /* Example queries */
    .example-queries-title {
        /* font-weight: bold; */
        margin-bottom: 10px;
    }

    .query-button {
        display: block;
        width: 100%;
        padding: 10px;
        margin: 5px 0;
        font-size: 14px;
        color: #007BFF;
        background-color: #f7faff;
        border: 1px solid #007BFF;
        border-radius: 24px;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s;
        text-decoration: none;
    }

    .query-button:hover {
        background-color: #e6f0ff;
    }

    .sources-section {
        margin-top: 20px;
    }
    .source-item {
        margin-bottom: 10px;
    }
    .feedback {
        font-family: Arial, sans-serif;
        display: flex;
        align-items: center;
        font-size: 16px;
    }
    .feedback span {
        margin-right: 10px;
    }
    .thumb-icon {
        cursor: pointer;
        width: 24px;
        height: 24px;
        margin-right: 5px;
        transition: transform 0.2s;
    }
    .thumb-icon:hover {
        transform: scale(1.1);
    }
  button {
    padding: 10px 20px;
    font-size: 16px;
    margin-top: 20px;
  }
  #output {
    font-size: 18px;
    margin-top: 20px;
    max-width: 600px;
    text-align: center;
  }
  /* Modal styling */
  .modal {
    display: none; /* Hidden by default */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    height: 64px;
    background-color: #fff;
    border: 1px solid #888;
    padding: 20px;
    width: 300px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-align: center;
  }
  .modal.show {
    display: block; /* Show the modal */
  }
  /* .input-group {
    max-width: 75% !important;
   } */
</style>
</head>
<body>

    <!-- Overlay for pushing down the background image -->
    <div class="overlay"></div>

    <div style="display: none;" class="popup-container">
        <!-- <button class="close-button" onclick="this.parentElement.style.display='none';">×</button> -->
        <img src="assets/images/ic_close.png" onclick="this.parentElement.style.display='none';" style="width: 16px;float: right;cursor: pointer;" />
        <br>
        <!-- <div class="popup-header">Humanity-Centered Design Plus (HCD+)</div> -->
        <div class="popup-description">
        Humanity-Centered Design, plus more (HCD+) is a helpful assistant providing information about Don Norman, his philosophy, and humanity-centered design. We aim to help you understand the impact of Don Norman’s work and how it contributes to creating user-friendly and meaningful designs. If you have any specific questions about Don Norman or related topics, feel free to ask!
        </div>
        <div class="example-queries-title">Example queries:</div>
        <a href="#" onclick="myFunctionR($(this).html());" class="query-button">Describe the principles of humanity-centered design.</a>
        <a href="#" onclick="myFunctionR($(this).html());" class="query-button">How has Don Norman influenced the world of design?</a>
        <a href="#" onclick="myFunctionR($(this).html());" class="query-button">What are the objectives of the Don Norman Design Awards?</a>
    </div>

    <!-- Main Content -->
    <main class="main-content d-flex flex-column align-items-center">
        <div class="first-before-chat">
            <img src="assets/images/bg.png" style="width:100%;height: 200px;" />
            <h4 style="color:#000000;text-align: center;padding: 30px;font-weight: 500;">Hello! I'm here to helpyou with your DNDA25 application.</h4>
            <p style="color:#818181a4;font-size: 13px;padding: 0 30px 0 30px;text-align: left;">By joining this chat, you confirm that you agree to The Don Norman Design Award Privacy Policy and understand that responses provided by DNDA .i’s AI Chatbot are for informational purposes only and responses are not legally binding.</p>
            <br><br><br>
        </div>


        <div style="display:none;" class="loading-container">
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
            <div class="loading-block"></div>
        </div>

        <div style="display:none;font-family: Arial, sans-serif;line-height: 1.6;margin: 0px;padding: 20px;color: black;text-align: left;width: 750px;max-width: 100%;" id="rasresult"></div>


        

        <!-- Search Bar -->
        <div class="ras-search input-group mb-3 w-95 w-md-50" style="max-width: 750px;">
            <textarea onkeyup="rasmyFunction()" id="rasSearch" style="height: 40px;padding: 10px 40px 0 30px;border-radius: 30px;font-size: 15px;" type="text" class="form-control" placeholder="Ask a detailed question to DNDA .i" aria-label="Ask a detailed question to DNDA .i"></textarea>
            <img class="send-button" style="margin-left: -39px;z-index: 10;width: 36px;height: 36px;margin-top: 2px;cursor: pointer;" class="img-btn" src="assets/images/ic_send_off.png" />
        </div>
    </main>

    <script>
        var postCount = 0;
        var isLoad=false;

        $("#rasSearch").keydown(function (event) {
            if (event.key === "Enter") {
                if (event.shiftKey) {
                    // Shift + Enter adds a new line
                    return true;
                } else {
                    // Enter submits the form
                    event.preventDefault();
                    var text = $('#rasSearch').val();
                    myFunctionR(text);
                }
            }
        });


        $.ajax({
            url: "api.php", 
            method: "GET",
            success: function(response) {
                // Parse the response into an object
                var parsedResponse = JSON.parse(response);

                console.log(parsedResponse);

                // Loop through the object using jQuery $.each()
                $.each(parsedResponse, function(key, value) {
                    $('.first-before-chat').hide();
                    $('#rasresult').hide();
                    $('.popup-container').hide();
                    $('.loading-container').show();

                    postCount++;


                    $('.loading-container').hide();
                    $('#rasresult').show();
                    var data = JSON.parse(value.text);
                    $('#rasSearch').val('');
                    $('#rasSearch').css('height','40px');
                    $('.ras-search > img').css('margin-top','2px');
                    $('.send-button').attr('src','assets/images/ic_send_off.png');


                    var html = data.result.answer;


                    const convertedHTML = convertMarkupToHTML(html);

                    html = '<div style="width: 100%;padding: 0px 0px 52px 0px;"><span style="float: right;background-color: #f0f0f0;padding: 11px;border-radius: 10px;font-weight: bold;">'+data.result.command+'</span></div><br>'+nl2br(convertedHTML);



                    // Add sources section
                    if(data.result.sources.length > 0)
                    {
                        var sourceItem = '';
                        var sourcesSection = `<div style="float: right;" class="feedback">\
                            <span style="color: #a6a4a4;">Was this answer helpful?</span>\
                            <img id="thumbUp" class="thumb-icon" src="assets/images/ic_thumb_up.png" alt="Thumbs Up" onclick="toggleThumbs('up')">\
                            <img id="thumbDown" class="thumb-icon" src="assets/images/ic_thumb_down.png" alt="Thumbs Down" onclick="toggleThumbs('down')">\
                        </div>`;
                        sourcesSection = sourcesSection + '<a style="cursor: pointer;color: blue;" onclick="$(this).hide();showSource('+postCount+')">Source</a><div style="display: none;" id="sources-section-'+postCount+'" class="sources-section">';
                        sourcesSection = sourcesSection + "<h5>Sources:</h5>";

                        
                        data.result.sources.forEach(source => {
                            if(source.metadata.title)
                            {
                                sourceItem = '<div class="source-item">';
                                sourceItem = sourceItem + `<a href="${source.metadata.source}" target="_blank">${source.metadata.title}</a>`;
                                sourceItem = sourceItem + '</div>';

                                sourcesSection = sourcesSection + sourceItem;
                            }
                            
                        });

                        sourcesSection = sourcesSection + '</div>';
                        html = html + sourcesSection;
                    }

                    $('#rasresult').append(html+'<br><br><br>');
                });

                $("html, body").animate({ scrollTop: $(document).height() - $(window).height() }, "slow");

                // console.log(response); // Debugging
            },
            error: function(xhr, status, error) {
                console.error("Error:", error);
            }
        });



        function rasmyFunction()
        {
            var str = $('#rasSearch').val();

            if(str.trim().length > 0)
            {
                $('.send-button').attr('src','assets/images/ic_send_on.png');
            }
            else
            {
                $('.send-button').attr('src','assets/images/ic_send_off.png');
            }
        }
        function showSource(id)
        {
            $('#sources-section-'+id).show();
        }
        function convertMarkupToHTML(markup) {
            // Convert headers
            let html = markup.replace(/###\s*(.+)/g, "<h3>$1</h3>");

            // Convert bold text wrapped with **
            html = html.replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>");

            // Convert list items starting with -
            html = html.replace(/^- (.+)/gm, "<li>$1</li>");
            
            // Wrap consecutive <li> elements in <ul> tags
            html = html.replace(/(<li>[\s\S]*?<\/li>)/g, "<ul>$1</ul>");
            html = html.replace(/<\/ul>\s*<ul>/g, "");  // Remove nested <ul> tags

            // Convert line breaks to <p> tags for paragraphs
            html = html.replace(/\n\s*\n/g, "</p><p>");
            html = `<p>${html}</p>`;  // Wrap entire text in <p> tags initially

            return html;
        }
        function nl2br (str, is_xhtml) {
            if (typeof str === 'undefined' || str === null) {
                return '';
            }
            var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
            return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
        }
        function myFunctionR(text) {

            if (text.trim().length==0) {
                return; 
            }


            //$('#rasresult').hide();
            //$('.popup-container').hide();
            //$('.loading-container').show();
            //$('.footer-img').hide();
            
            postCount++;

            html = '<div id="row'+postCount+'"><div style="width: 100%;padding: 0px 0px 52px 0px;"><span style="float: right;background-color: #f0f0f0;padding: 11px;border-radius: 10px;font-weight: bold;">'+text+'</span></div><br><div class="loading-container"><div class="loading-block"></div><div class="loading-block"></div></div></div>';
            $('#rasresult').append(html+'<br><br>');

            $('.first-before-chat').hide();
            $('#rasresult').show();

            window.scrollTo({
            top: ((document.documentElement.scrollHeight || document.body.scrollHeight)),
            behavior: 'smooth'
            });

            $('#rasSearch').val('');
            $('#rasSearch').css('height','40px');
            $('.ras-search > img').css('margin-top','2px');
            $('.send-button').attr('src','assets/images/ic_send_off.png');

            // Get form data
            const formData = {
                text: text,
                postCount: postCount
            };

            // Send POST request
            
            $.post('api.php', formData, function(response) {

                //$('.loading-container').hide();
                $('#rasresult').show();
                var data = JSON.parse(response);
                //alert('Response: ' + JSON.stringify(data.result.command));

                var html = data.result.answer;


                const convertedHTML = convertMarkupToHTML(html);

                html = '<div><div style="width: 100%;padding: 0px 0px 52px 0px;"><span style="float: right;background-color: #f0f0f0;padding: 11px;border-radius: 10px;font-weight: bold;">'+data.result.command+'</span></div><br>'+nl2br(convertedHTML);



                // Add sources section
                if(data.result.sources.length > 0)
                {
                    var sourceItem = '';
                    var sourcesSection = `<div style="float: right;" class="feedback">\
                        <span style="color: #a6a4a4;">Was this answer helpful?</span>\
                        <img id="thumbUp" class="thumb-icon" src="assets/images/ic_thumb_up.png" alt="Thumbs Up" onclick="toggleThumbs('up')">\
                        <img id="thumbDown" class="thumb-icon" src="assets/images/ic_thumb_down.png" alt="Thumbs Down" onclick="toggleThumbs('down')">\
                    </div>`;
                    sourcesSection = sourcesSection + '<a style="cursor: pointer;color: blue;" onclick="$(this).hide();showSource('+postCount+')">Source</a><div style="display: none;" id="sources-section-'+postCount+'" class="sources-section">';
                    sourcesSection = sourcesSection + "<h5>Sources:</h5>";

                    
                    data.result.sources.forEach(source => {
                        if(source.metadata.title)
                        {
                            sourceItem = '<div class="source-item">';
                            sourceItem = sourceItem + `<a href="${source.metadata.source}" target="_blank">${source.metadata.title}</a>`;
                            sourceItem = sourceItem + '</div>';

                            sourcesSection = sourcesSection + sourceItem;
                        }
                    });

                    sourcesSection = sourcesSection + '</div>';
                    html = html + sourcesSection;
                }

                $('#row'+postCount).html(html+'<br><br><br>');


            }).fail(function(error) {
                //$('.loading-container').hide();
                alert('Error: ' + JSON.stringify(error));
            });
        }

    </script>

    <script>
        $(document).ready(function(){
            $(".send-button").click(function(){
             var text = $('#rasSearch').val();

             myFunctionR(text);                
            });
        });
    </script>
    <script>
    const textarea = document.getElementById("rasSearch");

    // Adjust the height of the textarea to match its content
    textarea.addEventListener("input", () => {
        textarea.style.height = "auto"; // Reset height
        textarea.style.height = `${textarea.scrollHeight}px`; // Set to full height of content
        $('.ras-search > img').css('margin-top',`${textarea.scrollHeight-58}px`);
        console.log(textarea.value.length);
        if(textarea.value.length<=42)
        {
            textarea.style.height = "40px";
            $('.ras-search > img').css('margin-top','2px');
        }

        if(textarea.scrollHeight > 50 && textarea.scrollHeight < 75)
        {
            $('.ras-search > img').css('margin-top','10px');
        }

    });
    </script>

<script>
    function toggleThumbs(direction) {
        const thumbUp = document.getElementById('thumbUp');
        const thumbDown = document.getElementById('thumbDown');

        if (direction === 'up') {
            thumbUp.src = thumbUp.src.includes('ic_thumb_up.png') ? 'assets/images/ic_thumb_up_on.png' : 'assets/images/ic_thumb_up.png';
            thumbDown.src = 'assets/images/ic_thumb_down.png';
        } else if (direction === 'down') {
            thumbDown.src = thumbDown.src.includes('ic_thumb_down.png') ? 'assets/images/ic_thumb_down_on.png' : 'assets/images/ic_thumb_down.png';
            thumbUp.src = 'assets/images/ic_thumb_up.png';
        }
    }
</script>

<script>
  // Check for browser support
  if (!('webkitSpeechRecognition' in window)) {
    alert("This browser does not support speech recognition. Try Chrome.");
  } else {
    // Initialize Speech Recognition
    const recognition = new webkitSpeechRecognition();
    recognition.continuous = false; // Stop automatically after user stops speaking
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    const triggerPopupButton = document.getElementById("triggerPopup");
    const voiceModal = document.getElementById("voiceModal");
    const output = document.getElementById("output");

    // Show the popup/modal and start recording when the trigger button is clicked
    triggerPopupButton.addEventListener("click", () => {
      voiceModal.classList.add("show");
      output.textContent = "Listening...";
      recognition.start();
    });

    // When speech is recognized
    recognition.onresult = (event) => {
      let transcript = "";
      for (let i = event.resultIndex; i < event.results.length; i++) {
        transcript += event.results[i][0].transcript;
      }
      output.textContent = transcript;
    };

    // When recognition ends (auto-close the popup)
    recognition.onend = () => {
      voiceModal.classList.remove("show"); // Hide the modal when recording stops
      if (output.textContent === "Listening...") {
        output.textContent = "No speech detected.";
        alert("No speech detected.");
      } else {
        // output.textContent += " (Recording stopped)";
        //alert(output.textContent);
        myFunctionR(output.textContent);
      }
    };

    // Handle errors
    recognition.onerror = (event) => {
      console.error("Speech recognition error detected: ", event.error);
      alert("Error occurred: " + event.error);
      voiceModal.classList.remove("show"); // Hide modal on error
      output.textContent = "Error occurred. Please try again.";
    };
  }
</script>

    

    <!-- Bootstrap JS (optional for any interactive components) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
