<?php
/*
Template Name: Y25 Organization Tab Page
*/
require_once get_template_directory() . '/includes/role-permission-for-custom-page.php';

function getDecisionByCategory($category)
{
  $decision_list = ['Laureate', 'Ineligible', 'Move to Projects', 'Move to Organization'];
  if($category=='Laureate')
  {
    $decision_list = ['Laureate', 'Ineligible', 'Move to Projects', 'Move to Organization', 'Organization Recognition'];
  }
  return $decision_list;
}

$user_id = get_current_user_id();
$user_tab = get_transient("y25_organization_tab_$user_id");

global $wpdb;

$category = '';
if (isset($_GET['tab'])) {
    $category = trim(urldecode($_GET['tab']));
}

if($user_tab)
{
  $category = $user_tab;
  delete_transient("y25_organization_tab_$user_id");
}

// Decision list setup based on tab
$decision_list = getDecisionByCategory($category);

if ($_POST) {
  $table = $wpdb->prefix . 'frm_item_metas';
  $field_id = 375;

  $count = count($_POST['d']);

  $first_value = $category;
  $is_redirect = true;

  if($count > 0)
  {
    foreach ($_POST['d'] as $entry_id => $new_value) {
      if($first_value==$category)
      {
        $first_value = $new_value;
      }
      if($first_value != $new_value && $is_redirect && $first_value!=$category && $new_value!=$category)
      {
        $is_redirect = false;
      }

      $entry_id = (int) $entry_id;
      $new_value = sanitize_text_field($new_value); // sanitize input
      $now = current_time('mysql'); // WordPress current time


      // Check if meta exists
      $meta_exists = $wpdb->get_var(
          $wpdb->prepare(
              "SELECT id FROM $table WHERE item_id = %d AND field_id = %d LIMIT 1",
              $entry_id, $field_id
          )
      );

      if ($meta_exists) {
          // Update existing meta
          $wpdb->update(
              $table,
              [
                  'meta_value' => $new_value,
                  'created_at' => $now
              ],
              [
                  'item_id' => $entry_id,
                  'field_id' => $field_id
              ]
          );
      } else {
          // Insert new meta
          $wpdb->insert(
              $table,
              [
                  'item_id' => $entry_id,
                  'field_id' => $field_id,
                  'meta_value' => $new_value,
                  'created_at' => $now
              ]
          );
      }
    }

    if($is_redirect)
    {
      header('Location: /y25/organization/?tab='.urlencode($first_value));
      exit();
    }
  }

  
}
get_header();
?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
<style> 
.email {
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  padding: 20px;
  margin: 30px 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.email .email-cell {
  background-color: #ffffff;
  border-left: 5px solid #ccc;
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
}

.email .cell-red {
  border-left-color: #e74c3c;
  background-color: #fdecea;
}

.email .cell-green {
  border-left-color: #2ecc71;
  background-color: #eafaf1;
}

.email a {
  color: #3498db;
  text-decoration: none;
}

.email a:hover {
  text-decoration: underline;
}

.email p {
  margin-bottom: 12px;
}

.email hr {
  border: none;
  border-top: 2px solid #ccc;
  margin: 40px 0;
}

.email strong,
.email b {
  color: #c0392b;
}

@media (max-width: 600px) {
  .email {
    padding: 15px;
    font-size: 13px;
  }

  .email .email-cell {
    padding: 12px;
  }
}

.feedback {
  display: flex;
  gap: 15px;
  margin-bottom: 40px;
  align-items: flex-start;
  font-family: Arial, sans-serif;
}

.feedback-cell.cell-green {
  min-width: 32px;
  height: 32px;
  background-color: #e6f7e6 !important;
  border-radius: 5px;
  color: white;
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  line-height: 32px;
  padding: 0 5px;
}
.feedback-cell.cell-red {
  min-width: 32px;
  height: 32px;
  background-color: #ffe6e6 !important;
  border-radius: 20px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  line-height: 30px;
  padding: 0 5px;
  border: 2px solid red;
}


}
.feedback-cell.cell-red p{
  font-size: 8px;
  text-align: center;
}

.feedback-cell:nth-child(2) {
  font-weight: bold;
  font-size: 18px;
  width: 200px;
}

.feedback-cell:nth-child(2) a {
  font-weight: normal;
  margin-left: 5px;
  color: #2a73cc;
  text-decoration: none;
}

.feedback-cell.feedback-note {
  flex: 1;
  margin-left: 47px; /* aligns with text block */
  font-size: 16px;
  color: #333;
  margin-top: -14px;
}

.reviewers .cell-green {
  background-color: #e6f7e6 !important;
}

.reviewers .cell-red {
  background-color: #ffe6e6 !important;
  border: 1px solid red;
  border-radius: 15px;
  line-height: 2;
  max-width: 18px !important;
  margin-top: 5px;
}
.reviewers .cell-red p{
  font-size: 8px;
  text-align: center;
}

/* .feedback-cell img {
  width: 20px;
  height: auto;
  border-radius: 50%;
} */

    .top-header {
      /* display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap; */
    }

    .summary-cards {
      display: flex;
      gap: 10px;
    }

    .summary-card {
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 10px 16px;
      font-weight: bold;
      color: #666;
      font-size: 35px;
    }

    .summary-card span {
      font-size: 12px;
      font-weight: normal;
      float: right;
      line-height: 1.2;
      margin-left: 5px;
    }

    .summary-card.active {
      background-color: #2f67d8;
      color: #fff;
      border: none;
    }

    .summary-card.inactive {
      background-color: #f0f0f0;
      color: #ccc;
    }

    .filters {
      font-size: 15px;
      float: left;
    }

    .filters a {
      cursor: pointer;
    }

    .filters span {
      margin-right: 20px;
      font-weight: normal;
      color: #555;
    }

    .filters .active-tab {
      font-weight: bold;
      border-bottom: 2px solid black;
    }

    .search-group {
      display: flex;
      gap: 5px;
      margin-top: 10px;
    }

    .search-group input[type="text"] {
      padding: 8px 12px;
      font-size: 14px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 250px;
    }

    .search-group button {
      padding: 8px 18px;
      background: white;
      border: 2px solid #2f67d8;
      color: #2f67d8;
      font-weight: bold;
      font-size: 14px;
      border-radius: 8px;
      cursor: pointer;
    }

    .search-group button:hover {
      background: #2f67d8;
      color: white;
    }

    /* ==========================================================================
       FILTER AND BUTTON CONTROLS
       ========================================================================== */
    .filter-controls {
        display: flex;
        align-items: flex-end;
        gap: 15px;
        float: right;
        margin-top: -40px;
    }

    .filter-dropdown-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
        font-weight: normal;
    }

    .filter-dropdown {
        padding: 10px 15px;
        font-size: 15px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background: white;
        color: #333;
        cursor: pointer;
        min-width: 120px;
        height: 44px;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 16px;
        padding-right: 35px;
        box-sizing: border-box;
    }

    .filter-dropdown:focus {
        outline: none;
        border-color: #2f67d8;
    }

    .update-button {
        padding: 10px 20px;
        font-size: 15px;
        font-weight: bold;
        background: white;
        border: 2px solid #2f67d8;
        color: #2f67d8;
        border-radius: 8px;
        cursor: pointer;
        height: 44px;
        align-self: flex-end;
    }

    .update-button:hover {
        background-color: #2f67d8;
        color: white;
    }

    @media (max-width: 600px) {
      .top-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .search-group {
        width: 100%;
      }

      .search-group input {
        flex: 1;
      }
    }
    .et_pb_section_1_tb_header.et_pb_sticky_module .top-header
    {
      padding: 0 5% 0 5%;
    } 

    .table-container {
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
      overflow: hidden;
    }

    .ras-table {
      width: 100%;
      border-collapse: collapse;
      background: white;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      border-radius:10px;
    }

    .ras-table th, .ras-table td {
      padding: 12px;
      vertical-align: top;
      font-size: 18px;
      border: none;
    }
    .ras-table tr{
      /* border: 1px solid #ddd; */
    }

    .ras-table th {
      background-color: #2c3e50;
      color: white;
      text-align: left;
    }

    .ras-table .project-title {
      font-weight: bold;
      margin-bottom: 4px;
    }

    .ras-table .project-desc {
      font-size: 20px;
      color: #333;
    }

    .ras-table .actions {
      margin-top: 10px;
      font-size: 18px;
    }

    .ras-table .actions a {
      color: #007bff;
      text-decoration: none;
      margin-right: 8px;
    }

    .ras-table .actions a.disabled {
      color: #999;
      pointer-events: none;
    }

    .ras-table .eval-cell {
      display: flex;
      align-items: stretch;
      gap: 10px;
    }

    .ras-table .eval-score {
      font-weight: bold;
      color: #fff;
      min-width: 40px;
      text-align: center;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
    }

    .ras-table .eval-score.green { background-color: #4CAF50; }
    .ras-table .eval-score.red { background-color: #e74c3c; }
    .ras-table .eval-score.black { background-color: #333; }

    .ras-table .reviewers {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 20px;
    }

    .ras-table .reviewer {
      display: flex;
      align-items: center;
      margin-bottom: 2px;
    }

    .ras-table .reviewer::before {
      content: "✔️";
      margin-right: 5px;
      color: green;
    }

    .ras-table .reviewer.reject::before {
      content: "❌";
      color: red;
    }

    select {
      padding: 5px;
      width: 100%;
    }
    .eval-cell .reviewers img,.eval-cell .frm_image_option_size_small,.eval-cell .frm_has_image_options{
      width: 10px !important;
    }
    .eval-cell .Rtable{
      padding: 3px;
    }
    .eval-cell .Rtable .Rtable-cell:first-child {
      width: 20px !important;
      float: left;
      margin-right: 7px;
    }
    .eval-cell .reviewers{
      width: 100%;
    }
    .row{
      width:100%;
      height:80px;
    }
    .bg-gray{
      background-color: #f4f4f4
    }
  </style>
<script>
  function showFeedback(id,dohide=false) {
    if ($("#feedback_"+id).is(":visible") || dohide) {
      $("#feedback_"+id).hide();
      $("#show_feedback_"+id).html("Judge's Responses <i class='fas fa-chevron-down'></i>");
    } else {
      $("#feedback_"+id).show();
      showEmail(id,true);
      $("#show_feedback_"+id).html("Judge's Responses <i class='fas fa-chevron-up'></i>");
    }
  }
  function showEmail(id,dohide=false) {
    if ($("#email_"+id).is(":visible") || dohide) {
      $("#email_"+id).hide();
      $("#show_email_"+id).html('View Email');
    } else {
      $("#email_"+id).show();
      showFeedback(id,true);
      $("#show_email_"+id).html('Hide Email');
    }
  }
</script>
<div style="padding: 0% 5% 5% 5%;">
<div class="top-header">
  <div>
    <div class="row">
      <h1 style="float: left;">DNDA25 Applications</h1>
      <div style="float: right;" class="search-group">
        <!-- <form method="GET" action=""> -->
        <input type="hidden" name="tab" value="<?=$category;?>"/>  
        <input required value="<?=isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';?>" type="text" name="search" placeholder="Search..." />
        <!-- <button type="submit">Search</button> -->
        <!-- </form> -->
      </div>
    </div>
    
    <div class="summary-cards">
      <a href="/y25/applications/"><div class="summary-card"><spam id="count_total">0</spam> <span>Projects</span></div></a>
      <a href="/y25/educations/"><div class="summary-card"><spam id="count_total_1">0</spam> <span>HCD+<br>Education</span></div></a>
      <a href="/y25/organization/"><div class="summary-card active"><spam id="count_total_2">0</spam> <span>Organization<br>Recognition</span></div></a>
    </div>
    <div class="row">
      <br>
      
      <div class="filters">
        <a href="/y25/organization/"><span class="tabs <?=($category==''?'active-tab':'');?>"><spam id="tab1">0</spam> Organization Recognition</span></a>
        <a href="/y25/organization/?tab=Ineligible"><span class="tabs <?=($category=='Ineligible'?'active-tab':'');?>"><spam id="tab2">0</spam> Ineligible</span></a>
        <a href="/y25/organization/?tab=Laureate"><span class="tabs <?=($category=='Laureate'?'active-tab':'');?>"><spam id="tab3">0</spam> Laureate</span></a>
        <a href="/y25/organization/?tab=Sent%20Email"><span class="tabs <?=($category=='Sent Email'?'active-tab':'');?>"><spam id="tab4">0</spam> Sent Email</span></a>
      </div>
      <button form="frm_form_123" type="submit" class="update-button">Update List</button>
    </div>
  </div>
  <div class="table-container">
    
  </div>

    
</div>
<form id="frm_form_123" action="" method="post">
  <table class="ras-table">
      <thead>
        <tr>
          <th style="width: 120px;">Entry ID</th>
          <th style="width: 200px;">Decision</th>
          <th>Project Name</th>
          <th style="width: 200px;">Location</th>
          <th style="width: 200px;">Country</th>
          <th style="width: 200px;">Evaluation</th>
        </tr>
      </thead>
      <tbody>
          <?php
              // Load Formidable's entry class
              //$entries = FrmEntry::getAll(array('it.form_id' => 5), ' ORDER BY it.created_at ASC');

              $count_0=0;
              $count_1=0;
              $count_2=0;

              $tab1=0;
              $tab2=0;
              $tab3=0;
              $tab4=0;

              $count_0 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND (it.is_draft = 0 OR (it.is_draft = 1 AND im.meta_value='Full Project'))
                        ORDER BY it.id DESC
                        ",
                        5, // form_id
                  )
              );
              $count_1 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                        ORDER BY it.id DESC
                        ",
                        10, // form_id
                  )
              );
              $count_2 = $wpdb->get_var(
                  $wpdb->prepare(
                      "
                      SELECT COUNT(DISTINCT it.id)
                      FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                        ORDER BY it.id DESC
                        ",
                        16, // form_id
                  )
              );

              $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
              $results = [];

              if ( ! empty($search) && false) {
                  $results = $wpdb->get_results(
                      $wpdb->prepare(
                          "
                          SELECT DISTINCT it.id, it.is_draft
                          FROM {$wpdb->prefix}frm_items it
                          INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                          WHERE 
                            it.form_id = %d
                            AND it.is_draft = 0
                            AND im.meta_value LIKE %s
                          ORDER BY it.id DESC
                          ",
                          16, // form_id
                          '%' . $wpdb->esc_like($search) . '%'
                      )
                  );
              } 
              else 
              {
                  $results = $wpdb->get_results(
                    $wpdb->prepare(
                        "
                        SELECT DISTINCT it.id, it.is_draft
                        FROM {$wpdb->prefix}frm_items it
                        INNER JOIN {$wpdb->prefix}frm_item_metas im ON it.id = im.item_id
                        WHERE 
                          it.form_id = %d
                          AND it.is_draft = 0
                        ORDER BY it.id DESC
                        ",
                        16, // form_id
                    )
                );
              }

              
              
              // Now get full entries
              $bgcolor=true;
              foreach ( $results as $entry_row ) {
                  $entry_id = $entry_row->id;
                  $is_draft = $entry_row->is_draft;

                  $entry = FrmEntry::getOne( $entry_id );

                  // Get field values using field IDs or keys
                  $field_320 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 320); // Project Title
                  $field_377 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 377);
                  $field_321 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 321);
                  $field_322 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 322);

                  $field_375 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 375);
                  $field_446 = FrmEntryMeta::get_entry_meta_by_field($entry_id, 446);

                  $score = do_shortcode("[frm-math clean=1 decimal=1]([frm-stats id=251 type=count 236='Yes' 251='{$entry_id}'] / [frm-stats id=251 type=count 251='{$entry_id}'])[/frm-math]");
                  $score_cls = 'red';

                  //$feedback=do_shortcode("[display-frm-data id=2981 entry=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $feedback_data= do_shortcode('[display-frm-data id=3600 app="' . $entry_id . '" filter=1 drafts="both"]');
                  $email_data='';//do_shortcode("[display-frm-data id=3208 app=\"{$entry_id}\" filter=1 drafts=\"both\"]");
                  $reviews_data=do_shortcode("[display-frm-data id=3599 app=\"{$entry_id}\" filter=1 drafts=\"both\"]");

                  $email_data_count = preg_match_all('/class="email"/', $email_data);

                  $tab_value=$field_375;


                  $query = $wpdb->prepare(
                    "
                    SELECT target.meta_value
                    FROM {$wpdb->prefix}frm_items AS fi
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS match_field
                        ON fi.id = match_field.item_id AND match_field.field_id = %d
                    INNER JOIN {$wpdb->prefix}frm_item_metas AS target
                        ON fi.id = target.item_id AND target.field_id = %d
                    WHERE fi.form_id = %d
                      AND match_field.meta_value = %s
                    ",
                    259,
                    262,
                    12,
                    $entry_id
                );
                
                $meta_value = $wpdb->get_var($query);

                // if($tab_value=='Qualified' && $meta_value=='Yes')
                // {
                //   $tab_value = 'Finalist';
                // }
                $temp_field = $field_375;

                $dec=$field_375;

                switch ($temp_field) {
                  case "":
                    $tab1++;
                    break;
                  case "Ineligible":
                    $dec='Yes';
                    $tab2++;
                    break;
                  case "Laureate":
                    $dec = 'No';
                    $tab3++;
                    break;
                  default:
                    $tab3++;
                    $temp_field = 'Ineligible';
                }

                $decision_list = getDecisionByCategory($temp_field);

                if($field_446=='Sent Email')
                {
                  $tab4++;
                }

                if($category!=$temp_field && $category!='Sent Email' || ($category=='Sent Email' && $field_446!='Sent Email'))
                {
                  continue;
                }

                
                if(!empty($search))
                {
                  if(strpos(strtolower($field_320),strtolower($search))===false && strpos(strtolower($field_377),strtolower($search))===false && strpos(strtolower($field_322),strtolower($search))===false)
                  {
                    continue;
                  }
                }
                




                $bgcolor = !$bgcolor;


                  

                  //$reviews_data = FrmEntry::getOne( $entry_id, true );
          ?>    
        <tr data-num="<?=$tab_value;?>" id="row_<?=$entry_id;?>" style="<?=($bgcolor)?'border-top: 1px solid gray;background-color: #f4f4f4;':'';?>">
          <td style="width: 120px;"><strong>O2500<?=$entry_id;?></strong></td>
          <td style="width: 200px;">
            <select <?=($is_draft==1)?'disabled':''?> name="d[<?=$entry_id;?>]">
              <option value="">Select Decision</option>
              <?php foreach($decision_list as $row){?>
                <option <?=($field_375==$row)?'selected':'';?>><?=$row;?></option>
              <?php }?>
            </select>
          </td>
          <td>
            <div class="project-title"><strong><?php echo $field_320;?></strong></div>
            <?php echo $field_377;?>
          </td>
          <td style="width: 200px;"><?php echo $field_321; ?></td>
          <td style="width: 200px;"><?php echo $field_322; ?></td>
          <td style="width: 200px;">
            <div class="eval-cell">
              <div class="reviewers">
                <?=$reviews_data;?>
              </div>
            </div>
          </td>
        </tr>
        <tr id="email_<?php echo $entry_id;?>" style="display:none;" class="child-row">
          <td colspan="6"><?=$email_data;?></td>
        </tr>
        <tr id="feedback_<?php echo $entry_id; ?>" style="display:none;border-bottom: 1px solid gray;" class="child-row">
          <td colspan="6"><?=$feedback_data; ?></td>
        </tr>
        <tr id="rowchild_<?php echo $entry_id; ?>" style="<?=($bgcolor)?'border-bottom: 1px solid gray;background-color: #f4f4f4;':'';?>" class="child-row">
          <td></td>
          <td colspan="5">
            <div style="border-top: 1px solid #dcdbdb;">
              <div class="actions">
                
              <a href="javascript:void(0);" onClick="showFeedback(<?php echo $entry_id; ?>);" id="show_feedback_<?php echo $entry_id; ?>">Judge's Responses <i class="fas fa-chevron-down"></i></a> | 
                <a target="_blank" <?=($is_draft==1)?'disabled style="color:#908f8f"':'href="/y25/app-org/?app='.$entry_id.'"';?>>View Application</a>
                
                <?php if(''!=$field_375){?>
                  <span style="margin-left: 100px;"></span>
                  <a href="/y25/pq2-result-email/?app=<?php echo $entry_id; ?>&dec=<?php echo $dec; ?>">Compose Email</a> | 
                  <?php if($email_data_count == 0){
                    echo '<span style="color:#908f8f">View Email</span>';
                  }else{?>
                  <a href="javascript:void(0);" onClick="showEmail(<?php echo $entry_id; ?>);" id="show_email_<?php echo $entry_id; ?>">View Email</a>(<?=$email_data_count;?>)
                  <?php }?>
                <?php }?>

              </div>
            </div>
          </td>
        </tr>
      <?php } ?>  

      </tbody>
    </table>  
  </form>  
</div>
<script>
$(document).ready(function() {
  $('#count_total').html('<?=$count_0;?>');
  $('#count_total_1').html('<?=$count_1;?>');
  $('#count_total_2').html('<?=$count_2;?>');
  $('#tab1').html('<?=$tab1;?>');
  $('#tab2').html('<?=$tab2;?>');
  $('#tab3').html('<?=$tab3;?>');
  $('#tab4').html('<?=$tab4;?>');
});
</script>
<script>
$(document).ready(function() {
  var topHeader = $('.top-header').detach(); // Detach preserves events/data
  $('.et_pb_section_1_tb_header.et_pb_sticky_module').append(topHeader);
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function () {
  const button = document.querySelector('.frm_button_submit.frm_final_submit');
  if (button && button.textContent.trim() === 'Update') {
    button.textContent = 'Save Changes'; // <-- Change this to your desired label
  }
});
</script>
<script>
  var table;
  var childRows;
 jQuery(document).ready(function ($) {
  childRows = $('.child-row').detach();

  $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
    var requiredNum = window.currentNumFilter || '';
    if (!requiredNum) return true;

    var row = table.row(dataIndex).node();
    var num = $(row).data('num'); // get data-num attribute from the row
    return num == requiredNum;
  });

  table = $('.ras-table').DataTable({
    "fnDrawCallback": function(settings) {
      fillChildsB();
    },
    fixedHeader: true,
    paging: false,
    searching: true,
    info: false,
    ordering: true,
    lengthChange: false,
    autoWidth: false,
    stripeClasses: [],
    dom: 'lrtip', // removes the default search box
    columnDefs: [
      {
        targets: 1, // Column with dropdown
        render: function (data, type, row, meta) {
          if (type === 'filter' || type === 'sort') {
            // Extract the selected value from the select element
            var selected = $(data).find('option:selected').text();
            return selected;
          }
          return data; // keep the original HTML for display
        }
      }
    ]
  });

  fillChildsB();

  $('input[name="search"]').on('keyup', function () {
    table.search(this.value).draw();
    fillChildsB();
  });

  $('.tabs').on('click', function () {
  var tabToShow = $(this).data('tab');

  // Remove active class from all buttons
  $('.tab-btn').removeClass('active');
  $(this).addClass('active');

  // Hide all tabs, then show the selected one
  $('.tab-content').hide();
  $(tabToShow).show();
});

});
function fillChildsB()
{
  childRows.each(function () {
    // Get the ID like feedback_45 or email_45
    var id = this.id;
    
    // Extract the entry ID (e.g., 45)
    var entryId = id.split('_')[1];

    // Insert after the main entry row
    $('#row_' + entryId).after(this); // You must set id="row_45" in the main row
  });
}
function opentab(element, clear = false) {
  $('.tabs').removeClass('active-tab');
  $(element).find('.tabs').addClass('active-tab');

  var val = $(element).find('.tabs').clone().children().remove().end().text().trim();

  if (!clear) {
    window.currentNumFilter = val;
  } else {
    window.currentNumFilter = '';
  }

  table.draw();
  fillChildsB();
}
</script>
<?php get_footer(); ?>